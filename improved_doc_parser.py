#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的.doc文件解析器
专门针对婴幼儿评估表的格式进行优化
"""

import os
import glob
import json
import re
from collections import defaultdict

def extract_meaningful_text_from_doc(file_path):
    """从.doc文件中提取有意义的中文文本"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # 尝试多种编码方式
        text_candidates = []
        
        # 方法1: 直接搜索中文字符模式
        chinese_pattern = rb'[\xe4-\xe9][\x80-\xbf][\x80-\xbf]'  # UTF-8中文字符模式
        chinese_bytes = re.findall(chinese_pattern, content)
        
        if chinese_bytes:
            try:
                chinese_text = b''.join(chinese_bytes).decode('utf-8', errors='ignore')
                text_candidates.append(('UTF-8直接提取', chinese_text))
            except:
                pass
        
        # 方法2: 查找连续的可打印ASCII和中文字符
        # 在Word文档中，文本通常以特定模式存储
        text_pattern = rb'[\x20-\x7e\xe4-\xe9][\x80-\xbf]*'
        text_segments = re.findall(text_pattern, content)
        
        for encoding in ['utf-8', 'gbk', 'gb2312']:
            try:
                decoded_segments = []
                for segment in text_segments:
                    try:
                        decoded = segment.decode(encoding, errors='ignore')
                        if len(decoded) > 2 and any('\u4e00' <= c <= '\u9fff' for c in decoded):
                            decoded_segments.append(decoded)
                    except:
                        continue
                
                if decoded_segments:
                    combined_text = ' '.join(decoded_segments)
                    text_candidates.append((f'{encoding}分段提取', combined_text))
            except:
                continue
        
        # 方法3: 查找特定的Word文档文本标记
        # Word文档中文本通常在特定的结构中
        word_text_patterns = [
            rb'\x00([^\x00]{3,50})\x00',  # 零字节分隔的文本
            rb'[\x20-\x7e\u4e00-\u9fff]{5,}',  # 连续的可读字符
        ]
        
        for pattern in word_text_patterns:
            matches = re.findall(pattern, content)
            for encoding in ['utf-8', 'utf-16le', 'gbk']:
                try:
                    decoded_matches = []
                    for match in matches:
                        if isinstance(match, bytes):
                            decoded = match.decode(encoding, errors='ignore')
                        else:
                            decoded = match
                        
                        # 过滤掉明显的乱码
                        if (len(decoded) > 3 and 
                            any('\u4e00' <= c <= '\u9fff' for c in decoded) and
                            not all(ord(c) < 32 or ord(c) > 126 for c in decoded if ord(c) < 256)):
                            decoded_matches.append(decoded)
                    
                    if decoded_matches:
                        combined_text = '\n'.join(decoded_matches)
                        text_candidates.append((f'模式匹配-{encoding}', combined_text))
                except:
                    continue
        
        # 选择最好的候选文本
        best_text = ""
        best_score = 0
        best_method = "无"
        
        for method, text in text_candidates:
            if not text:
                continue
                
            # 计算文本质量分数
            chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
            total_chars = len(text)
            
            if total_chars == 0:
                continue
                
            chinese_ratio = chinese_chars / total_chars
            
            # 检查是否包含评估表相关关键词
            keywords = ['宝宝', '能力', '发展', '评估', '月龄', '运动', '语言', '认知', '社会', '适应']
            keyword_count = sum(1 for keyword in keywords if keyword in text)
            
            # 综合评分
            score = chinese_ratio * 100 + keyword_count * 10 + min(chinese_chars, 1000)
            
            if score > best_score:
                best_score = score
                best_text = text
                best_method = method
        
        return best_text, best_method, best_score
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None, None, 0

def parse_assessment_structure(text, filename):
    """解析评估表的结构化内容"""
    if not text:
        return None
    
    # 提取月龄信息
    age_match = re.search(r'(\d+(?:-\d+)?)月龄', filename)
    age_group = age_match.group(1) if age_match else "未知"
    
    # 清理文本
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    clean_lines = []
    
    for line in lines:
        # 移除明显的乱码行
        if (len(line) > 3 and 
            any('\u4e00' <= c <= '\u9fff' for c in line) and
            len([c for c in line if '\u4e00' <= c <= '\u9fff']) / len(line) > 0.3):
            clean_lines.append(line)
    
    # 识别评估项目
    assessment_items = []
    current_section = None
    
    for line in clean_lines:
        # 识别章节标题
        if any(keyword in line for keyword in ['身体运动', '探索与操作', '听觉言语', '社会适应']):
            current_section = line
            continue
        
        # 识别评估题目（通常包含"宝宝"和"能"/"不能"）
        if ('宝宝' in line and ('能' in line or '不能' in line)) or re.match(r'^\d+', line):
            # 清理题目文本
            clean_item = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffefa-zA-Z0-9\s\.\,\!\?\:\;\(\)\[\]（）、。，！？：；]', '', line)
            if len(clean_item) > 10:  # 只保留足够长的有意义文本
                assessment_items.append({
                    'section': current_section,
                    'item': clean_item.strip(),
                    'original_line': line
                })
    
    # 提取关键信息
    keywords_found = []
    for keyword in ['大运动', '精细运动', '语言', '认知', '社会', '情感', '自理', '发展', '能力', '评估']:
        if keyword in text:
            keywords_found.append(keyword)
    
    # 识别发展领域
    domains = []
    domain_patterns = {
        '身体运动与控制': ['身体运动', '运动', '控制', '抬头', '翻身', '坐立', '爬行', '站立'],
        '探索与操作': ['探索', '操作', '抓握', '手指', '玩具', '精细'],
        '听觉言语': ['听觉', '言语', '语言', '声音', '说话', '叫'],
        '社会适应': ['社会', '适应', '微笑', '哭', '熟悉', '陌生']
    }
    
    for domain, patterns in domain_patterns.items():
        if any(pattern in text for pattern in patterns):
            domains.append(domain)
    
    return {
        'filename': filename,
        'age_group': age_group,
        'text_length': len(text),
        'clean_lines_count': len(clean_lines),
        'assessment_items': assessment_items,
        'keywords_found': keywords_found,
        'domains_identified': domains,
        'sample_text': text[:500] if text else "",  # 保存前500字符作为样本
        'quality_indicators': {
            'has_baby_references': '宝宝' in text,
            'has_ability_terms': any(term in text for term in ['能', '不能', '会', '可以']),
            'has_age_reference': '月龄' in text,
            'chinese_char_ratio': len([c for c in text if '\u4e00' <= c <= '\u9fff']) / len(text) if text else 0
        }
    }

def create_improved_dataset():
    """创建改进的数据集"""
    assessment_dir = "assessment_forms"
    doc_files = glob.glob(os.path.join(assessment_dir, "*.doc"))
    doc_files.sort()
    
    print("🔍 改进的.doc文件解析")
    print("=" * 50)
    
    dataset = {
        'metadata': {
            'dataset_name': '婴幼儿综合能力发展评估量表数据集（改进版）',
            'description': '使用改进算法提取的评估表内容，专门优化中文文本识别',
            'source': '广东省残疾人康复中心',
            'total_files': len(doc_files),
            'extraction_date': '2025-01-25',
            'format_version': '2.0',
            'extraction_method': '多模式中文文本提取'
        },
        'documents': [],
        'extraction_summary': {
            'successful': 0,
            'failed': 0,
            'total_text_length': 0,
            'average_quality_score': 0,
            'methods_used': defaultdict(int)
        }
    }
    
    total_quality_score = 0
    
    for i, doc_path in enumerate(doc_files, 1):
        filename = os.path.basename(doc_path)
        print(f"\n[{i}/{len(doc_files)}] 处理: {filename}")
        
        # 提取文本
        text, method, quality_score = extract_meaningful_text_from_doc(doc_path)
        
        if text and quality_score > 10:  # 设置质量阈值
            # 解析结构
            parsed_content = parse_assessment_structure(text, filename)
            
            if parsed_content:
                parsed_content['extraction_method'] = method
                parsed_content['quality_score'] = quality_score
                
                dataset['documents'].append(parsed_content)
                dataset['extraction_summary']['successful'] += 1
                dataset['extraction_summary']['methods_used'][method] += 1
                dataset['extraction_summary']['total_text_length'] += len(text)
                total_quality_score += quality_score
                
                print(f"  ✅ 成功 - 方法: {method}")
                print(f"  📊 质量分数: {quality_score:.1f}")
                print(f"  📝 文本长度: {len(text)} 字符")
                print(f"  🎯 评估项目: {len(parsed_content['assessment_items'])} 个")
                print(f"  🧠 识别领域: {', '.join(parsed_content['domains_identified'])}")
                
                # 显示样本文本
                if parsed_content['sample_text']:
                    sample = parsed_content['sample_text'][:100]
                    print(f"  📄 样本: {sample}...")
            else:
                dataset['extraction_summary']['failed'] += 1
                print(f"  ❌ 解析失败")
        else:
            dataset['extraction_summary']['failed'] += 1
            print(f"  ❌ 提取失败 - 质量分数: {quality_score:.1f}")
    
    # 计算平均质量分数
    if dataset['extraction_summary']['successful'] > 0:
        dataset['extraction_summary']['average_quality_score'] = total_quality_score / dataset['extraction_summary']['successful']
    
    # 保存结果
    output_file = 'improved_assessment_dataset.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    # 生成统计报告
    print("\n" + "=" * 50)
    print("📊 改进解析结果总结")
    print("=" * 50)
    print(f"✅ 成功解析: {dataset['extraction_summary']['successful']} 个文件")
    print(f"❌ 解析失败: {dataset['extraction_summary']['failed']} 个文件")
    print(f"📈 成功率: {dataset['extraction_summary']['successful']/len(doc_files)*100:.1f}%")
    print(f"📝 总文本长度: {dataset['extraction_summary']['total_text_length']:,} 字符")
    print(f"⭐ 平均质量分数: {dataset['extraction_summary']['average_quality_score']:.1f}")
    
    print(f"\n🔧 使用的提取方法:")
    for method, count in dataset['extraction_summary']['methods_used'].items():
        print(f"  {method}: {count} 个文件")
    
    print(f"\n📄 生成文件: {output_file}")
    
    return dataset

def main():
    """主函数"""
    print("🚀 启动改进的.doc文件解析器")
    print("专门针对婴幼儿评估表进行优化")
    print()
    
    dataset = create_improved_dataset()
    
    print(f"\n✅ 改进解析完成！")
    if dataset['extraction_summary']['successful'] > 0:
        print(f"🎯 成功提取了 {dataset['extraction_summary']['successful']} 个评估表的内容")
        print(f"📊 平均质量分数: {dataset['extraction_summary']['average_quality_score']:.1f}")
    else:
        print("⚠️  未能成功提取任何内容，可能需要进一步优化算法")

if __name__ == "__main__":
    main()
