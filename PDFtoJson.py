import requests
import time
import os
import glob
import json
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 获取当前目录下的所有PDF文件
pdf_files = glob.glob("*.pdf")
print(f"找到 {len(pdf_files)} 个PDF文件: {pdf_files}")

public_key = "public_key_6a6aa95293f50da326424ead8f4c1887"
secret_key = "secret_key_ed254d06854240be93ff624d07262e5e"

AUTH_URL = "https://api-server.compdf.com/server/v1/oauth/token"
CREATE_TASK_URL = "https://api-server.compdf.com/server/v1/task"
UPLOAD_FILE_URL = "https://api-server.compdf.com/server/v1/file/upload"
EXECUTE_TASK_URL = "https://api-server.compdf.com/server/v1/execute/start"
GET_TASK_INFO_URL = "https://api-server.compdf.com/server/v1/task/taskInfo"

def create_session_with_retry():
    """创建带重试机制的session"""
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

def safe_json_response(response, context=""):
    """安全地解析JSON响应"""
    try:
        if response.status_code != 200:
            print(f"HTTP错误 {context}: {response.status_code} - {response.text}")
            return None

        if not response.text.strip():
            print(f"空响应 {context}")
            return None

        return response.json()
    except json.JSONDecodeError as e:
        print(f"JSON解析错误 {context}: {e}")
        print(f"响应内容: {response.text[:200]}...")
        return None
    except Exception as e:
        print(f"其他错误 {context}: {e}")
        return None

def authenticate(session):
    """获取认证token"""
    auth_payload = {"publicKey": public_key, "secretKey": secret_key}
    try:
        auth_response = session.post(AUTH_URL, json=auth_payload, timeout=30)
        auth_data = safe_json_response(auth_response, "认证")
        if not auth_data or "data" not in auth_data:
            print("认证失败：无效的响应数据")
            return None

        bearer_token = "Bearer " + auth_data["data"]["accessToken"]
        print("bearerToken:", bearer_token)
        return bearer_token
    except Exception as e:
        print(f"认证过程中出错: {e}")
        return None

def process_pdf_to_json(pdf_path, bearer_token, session):
    """处理单个PDF文件转换为JSON"""
    print(f"\n开始处理文件: {pdf_path}")

    try:
        # 2. Create Task
        create_task_response = session.get(CREATE_TASK_URL + "/pdf/json?language=2",
                                         headers={"Authorization": bearer_token}, timeout=30)
        create_task_data = safe_json_response(create_task_response, "创建任务")
        if not create_task_data or "data" not in create_task_data:
            print("创建任务失败")
            return None

        task_id = create_task_data["data"]["taskId"]
        print(f"taskId: {task_id}")

        # 3. Upload File
        with open(pdf_path, "rb") as file:
            files = {
                'file': (os.path.basename(pdf_path), file, 'application/octet-stream'),
            }
            upload_payload = {
                "taskId": task_id,
                "parameter": '{  "type": 1, "isAllowOcr":0, "isContainOcrBg":0}',
                "language": "2"
            }
            upload_file_response = session.post(UPLOAD_FILE_URL, files=files, data=upload_payload,
                                              headers={"Authorization": bearer_token}, timeout=60)
            upload_data = safe_json_response(upload_file_response, "上传文件")
            if not upload_data:
                print("文件上传失败")
                return None
            print("Upload File Result:", upload_data)

        # 4. Execute Task
        execute_task_response = session.get(EXECUTE_TASK_URL + "?language=2&taskId=" + task_id,
                                          headers={"Authorization": bearer_token}, timeout=30)
        execute_data = safe_json_response(execute_task_response, "执行任务")
        if not execute_data:
            print("执行任务失败")
            return None
        print("Execute Task Result:", execute_data)

        # 5. Get Task Information
        max_attempts = 60  # 最多等待60秒
        attempt = 0
        while attempt < max_attempts:
            time.sleep(1)
            attempt += 1

            task_info_response = session.get(GET_TASK_INFO_URL + "?taskId=" + task_id,
                                           headers={"Authorization": bearer_token}, timeout=30)
            task_info_data = safe_json_response(task_info_response, "获取任务信息")

            if not task_info_data or "data" not in task_info_data:
                print(f"获取任务信息失败 (尝试 {attempt}/{max_attempts})")
                continue

            task_status = task_info_data["data"]["taskStatus"]
            print(f"任务状态: {task_status} (尝试 {attempt}/{max_attempts})")

            if task_status == "TaskFinish":
                print("Task Information:", task_info_data)

                # 保存JSON结果到文件
                json_filename = os.path.splitext(pdf_path)[0] + ".json"
                with open(json_filename, 'w', encoding='utf-8') as json_file:
                    json.dump(task_info_data, json_file, ensure_ascii=False, indent=2)
                print(f"JSON结果已保存到: {json_filename}")

                return task_info_data
            elif task_status == "TaskError":
                print(f"任务失败: {task_info_data}")
                return None

        print(f"任务超时 (等待了 {max_attempts} 秒)")
        return None

    except Exception as e:
        print(f"处理过程中出错: {e}")
        return None

# 主程序
if __name__ == "__main__":
    # 创建带重试机制的session
    session = create_session_with_retry()

    # 1. Authentication
    bearer_token = authenticate(session)
    if not bearer_token:
        print("认证失败，程序退出")
        exit(1)

    # 处理每个PDF文件
    success_count = 0
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"\n{'='*50}")
        print(f"处理第 {i}/{len(pdf_files)} 个文件")
        print(f"{'='*50}")

        result = process_pdf_to_json(pdf_file, bearer_token, session)
        if result:
            print(f"✅ 成功处理: {pdf_file}")
            success_count += 1
        else:
            print(f"❌ 处理失败: {pdf_file}")

        # 在处理下一个文件前稍作等待
        if i < len(pdf_files):  # 不是最后一个文件
            print("等待3秒后处理下一个文件...")
            time.sleep(3)

    print(f"\n{'='*50}")
    print(f"所有PDF文件处理完成！")
    print(f"成功: {success_count}/{len(pdf_files)} 个文件")
    print(f"{'='*50}")