#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿综合能力发展评估量表分析器
分析评估表内容，为构建0-3岁婴幼儿育幼领域文本数据集提供基础
"""

import os
import glob
import json
import re
from collections import defaultdict, Counter
import pandas as pd

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import subprocess
    ANTIWORD_AVAILABLE = True
except ImportError:
    ANTIWORD_AVAILABLE = False

def extract_text_from_doc_with_antiword(file_path):
    """使用antiword从.doc文件中提取文本"""
    try:
        result = subprocess.run(['antiword', file_path],
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            return result.stdout
        else:
            return None
    except Exception as e:
        print(f"使用antiword读取 {file_path} 时出错: {e}")
        return None

def extract_text_from_doc_with_textract(file_path):
    """使用textract从.doc文件中提取文本"""
    try:
        import textract
        text = textract.process(file_path).decode('utf-8')
        return text
    except Exception as e:
        print(f"使用textract读取 {file_path} 时出错: {e}")
        return None

def extract_text_from_doc_simple(file_path):
    """简单的.doc文件文本提取（基于字符串搜索）"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()

        # 尝试提取可读的中文文本
        text_parts = []

        # 将字节转换为字符串，忽略错误
        try:
            text = content.decode('utf-8', errors='ignore')
        except:
            text = content.decode('gbk', errors='ignore')

        # 使用正则表达式提取中文文本
        chinese_text = re.findall(r'[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]+', text)

        # 过滤掉太短的文本片段
        meaningful_text = [t for t in chinese_text if len(t) > 2]

        return '\n'.join(meaningful_text) if meaningful_text else None

    except Exception as e:
        print(f"简单读取 {file_path} 时出错: {e}")
        return None

def extract_text_from_word_doc(file_path):
    """从Word文档中提取文本内容（支持.doc和.docx）"""
    content = {
        'paragraphs': [],
        'tables': [],
        'full_text': ''
    }

    # 检查文件扩展名
    if file_path.lower().endswith('.docx') and DOCX_AVAILABLE:
        try:
            doc = Document(file_path)

            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content['paragraphs'].append(paragraph.text.strip())

            # 提取表格内容
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_data.append(cell_text)
                    if row_data:
                        table_data.append(row_data)
                if table_data:
                    content['tables'].append(table_data)

            # 合并所有文本
            all_text = []
            all_text.extend(content['paragraphs'])
            for table in content['tables']:
                for row in table:
                    all_text.extend(row)
            content['full_text'] = '\n'.join(all_text)

            return content

        except Exception as e:
            print(f"读取.docx文件 {file_path} 时出错: {e}")

    # 处理.doc文件
    elif file_path.lower().endswith('.doc'):
        # 尝试多种方法读取.doc文件
        text = None

        # 方法1: 尝试使用antiword
        text = extract_text_from_doc_with_antiword(file_path)

        # 方法2: 如果antiword失败，尝试textract
        if not text:
            text = extract_text_from_doc_with_textract(file_path)

        # 方法3: 如果都失败，使用简单的字符串提取
        if not text:
            text = extract_text_from_doc_simple(file_path)

        if text:
            # 简单地将文本按行分割作为段落
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            content['paragraphs'] = lines
            content['full_text'] = text
            return content
        else:
            print(f"所有方法都无法读取文件: {file_path}")

    return None

def extract_age_from_filename(filename):
    """从文件名中提取月龄信息"""
    # 匹配各种月龄格式
    patterns = [
        r'(\d+)-(\d+)月龄',  # 0-1月龄
        r'(\d+)月龄',        # 2月龄
        r'(\d+)个月',        # 7个月
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            if len(match.groups()) == 2:  # 范围格式
                return f"{match.group(1)}-{match.group(2)}月龄"
            else:  # 单个月龄
                return f"{match.group(1)}月龄"
    return "未知月龄"

def analyze_assessment_content(content, filename):
    """分析评估表内容结构"""
    analysis = {
        'filename': filename,
        'age_group': extract_age_from_filename(filename),
        'total_paragraphs': len(content['paragraphs']),
        'total_tables': len(content['tables']),
        'text_length': len(content['full_text']),
        'development_domains': [],
        'assessment_items': [],
        'keywords': [],
        'structure_analysis': {}
    }
    
    # 分析发展领域
    domain_keywords = {
        '大运动': ['大运动', '粗大运动', '运动发展', '肢体运动'],
        '精细运动': ['精细运动', '手部动作', '手眼协调', '操作能力'],
        '语言': ['语言', '言语', '表达', '理解', '词汇', '沟通'],
        '认知': ['认知', '智力', '思维', '记忆', '注意', '学习'],
        '社会性': ['社会', '社交', '情感', '人际', '交往', '合作'],
        '自理': ['自理', '生活', '独立', '照顾', '日常'],
        '感知觉': ['视觉', '听觉', '触觉', '感知', '知觉']
    }
    
    text_lower = content['full_text'].lower()
    for domain, keywords in domain_keywords.items():
        for keyword in keywords:
            if keyword in content['full_text']:
                if domain not in analysis['development_domains']:
                    analysis['development_domains'].append(domain)
                break
    
    # 提取评估项目
    assessment_patterns = [
        r'(\d+[\.\、].*?)(?=\d+[\.\、]|$)',  # 编号项目
        r'(能够.*?)(?=能够|$)',              # 能力描述
        r'(会.*?)(?=会|$)',                  # 技能描述
    ]
    
    for pattern in assessment_patterns:
        matches = re.findall(pattern, content['full_text'], re.DOTALL)
        for match in matches:
            item = match.strip()
            if len(item) > 5 and len(item) < 200:  # 过滤太短或太长的项目
                analysis['assessment_items'].append(item)
    
    # 提取关键词
    # 简单的关键词提取（基于词频）
    words = re.findall(r'[\u4e00-\u9fff]+', content['full_text'])
    word_freq = Counter(words)
    # 过滤常见词和单字
    common_words = {'的', '了', '在', '是', '有', '和', '与', '或', '能', '会', '可', '不', '月', '岁', '个', '项', '分', '表', '评', '估', '发', '展', '能', '力'}
    analysis['keywords'] = [word for word, freq in word_freq.most_common(20) 
                           if len(word) > 1 and word not in common_words]
    
    # 结构分析
    analysis['structure_analysis'] = {
        'has_tables': len(content['tables']) > 0,
        'table_count': len(content['tables']),
        'avg_table_rows': sum(len(table) for table in content['tables']) / len(content['tables']) if content['tables'] else 0,
        'paragraph_count': len(content['paragraphs']),
        'avg_paragraph_length': sum(len(p) for p in content['paragraphs']) / len(content['paragraphs']) if content['paragraphs'] else 0
    }
    
    return analysis

def analyze_all_assessment_forms():
    """分析所有评估表文件"""
    assessment_dir = "assessment_forms"
    
    # 只分析综合能力发展评估量表
    pattern = os.path.join(assessment_dir, "*婴幼儿综合能力发展评估量表*.doc")
    files = glob.glob(pattern)
    files.sort()
    
    print(f"找到 {len(files)} 个综合能力发展评估表文件")
    print("=" * 60)
    
    all_analyses = []
    
    for i, file_path in enumerate(files, 1):
        filename = os.path.basename(file_path)
        print(f"[{i}/{len(files)}] 分析文件: {filename}")
        
        content = extract_text_from_word_doc(file_path)
        if content:
            analysis = analyze_assessment_content(content, filename)
            all_analyses.append(analysis)
            
            # 显示基本信息
            print(f"  月龄: {analysis['age_group']}")
            print(f"  文本长度: {analysis['text_length']} 字符")
            print(f"  发展领域: {', '.join(analysis['development_domains'])}")
            print(f"  评估项目数: {len(analysis['assessment_items'])}")
            print(f"  表格数: {analysis['total_tables']}")
            print()
        else:
            print(f"  ❌ 无法读取文件")
            print()
    
    return all_analyses

def generate_dataset_analysis(analyses):
    """生成数据集分析报告"""
    print("=" * 60)
    print("📊 数据集构建可行性分析")
    print("=" * 60)
    
    # 统计信息
    total_files = len(analyses)
    total_text_length = sum(a['text_length'] for a in analyses)
    total_assessment_items = sum(len(a['assessment_items']) for a in analyses)
    
    print(f"📁 文件统计:")
    print(f"  总文件数: {total_files}")
    print(f"  总文本长度: {total_text_length:,} 字符")
    print(f"  总评估项目数: {total_assessment_items}")
    print(f"  平均每文件文本长度: {total_text_length//total_files:,} 字符")
    print()
    
    # 月龄覆盖分析
    age_groups = [a['age_group'] for a in analyses]
    print(f"📅 月龄覆盖:")
    for age in sorted(set(age_groups)):
        count = age_groups.count(age)
        print(f"  {age}: {count} 个文件")
    print()
    
    # 发展领域分析
    all_domains = []
    for a in analyses:
        all_domains.extend(a['development_domains'])
    domain_freq = Counter(all_domains)
    
    print(f"🧠 发展领域覆盖:")
    for domain, freq in domain_freq.most_common():
        print(f"  {domain}: {freq} 个文件 ({freq/total_files*100:.1f}%)")
    print()
    
    # 关键词分析
    all_keywords = []
    for a in analyses:
        all_keywords.extend(a['keywords'])
    keyword_freq = Counter(all_keywords)
    
    print(f"🔑 高频关键词 (Top 15):")
    for keyword, freq in keyword_freq.most_common(15):
        print(f"  {keyword}: {freq} 次")
    print()
    
    # 数据集构建建议
    print("💡 数据集构建建议:")
    print("1. 数据量评估:")
    print(f"   - 当前有 {total_files} 个评估表，覆盖 0-34 月龄")
    print(f"   - 总计 {total_assessment_items} 个评估项目")
    print(f"   - 文本总量约 {total_text_length/1000:.1f}K 字符")
    print()
    
    print("2. 数据质量:")
    print(f"   - 发展领域覆盖全面: {len(domain_freq)} 个主要领域")
    print(f"   - 月龄分布连续: 0-34月龄基本覆盖")
    print(f"   - 专业术语丰富: {len(keyword_freq)} 个不同关键词")
    print()
    
    print("3. 数据集应用场景:")
    print("   - 婴幼儿发展里程碑知识库")
    print("   - 育儿指导问答系统")
    print("   - 发展评估智能助手")
    print("   - 早期干预建议生成")
    print()
    
    print("4. 数据增强建议:")
    print("   - 结合其他评估工具扩充数据")
    print("   - 添加实际案例和评估记录")
    print("   - 补充育儿知识和指导建议")
    print("   - 增加多模态数据（图片、视频描述）")
    
    return {
        'total_files': total_files,
        'total_text_length': total_text_length,
        'total_assessment_items': total_assessment_items,
        'age_coverage': list(set(age_groups)),
        'domain_coverage': list(domain_freq.keys()),
        'top_keywords': [kw for kw, _ in keyword_freq.most_common(20)]
    }

def save_analysis_results(analyses, summary):
    """保存分析结果"""
    # 保存详细分析结果
    with open('assessment_analysis_detailed.json', 'w', encoding='utf-8') as f:
        json.dump(analyses, f, ensure_ascii=False, indent=2)
    
    # 保存汇总结果
    with open('assessment_analysis_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    # 创建CSV格式的数据集预览
    df_data = []
    for analysis in analyses:
        for item in analysis['assessment_items'][:5]:  # 每个文件取前5个项目作为示例
            df_data.append({
                'age_group': analysis['age_group'],
                'domains': ', '.join(analysis['development_domains']),
                'assessment_item': item,
                'source_file': analysis['filename']
            })
    
    df = pd.DataFrame(df_data)
    df.to_csv('assessment_dataset_preview.csv', index=False, encoding='utf-8-sig')
    
    print("📄 分析结果已保存:")
    print("  - assessment_analysis_detailed.json (详细分析)")
    print("  - assessment_analysis_summary.json (汇总统计)")
    print("  - assessment_dataset_preview.csv (数据集预览)")

def main():
    """主函数"""
    print("🔍 开始分析婴幼儿综合能力发展评估量表")
    print("目标: 评估构建0-3岁婴幼儿育幼领域文本数据集的可行性")
    print()
    
    # 分析所有评估表
    analyses = analyze_all_assessment_forms()
    
    if not analyses:
        print("❌ 没有成功分析任何文件")
        return
    
    # 生成数据集分析报告
    summary = generate_dataset_analysis(analyses)
    
    # 保存结果
    save_analysis_results(analyses, summary)
    
    print("\n✅ 分析完成！")

if __name__ == "__main__":
    main()
